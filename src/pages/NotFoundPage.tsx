import React from "react";
import {
	StyleSheet,
	Text,
	View,
	TouchableOpacity,
	SafeAreaView,
	Image,
} from "react-native";
import { useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { RootStackParamList } from "../app/index";
import { scale } from "../utils/helpers/dimensionScale.helper";
import { GLOBAL_STYLES } from "../styles/globalStyles";

/**
 * NotFoundPage Component
 *
 * Displays a 404 error page with a message and a button to navigate back to home
 */

// Layout configuration - adjust to change the split ratio
const LAYOUT = {
	CONTENT_FLEX: 35, // Percentage for content side
	IMAGE_FLEX: 65, // Percentage for image side
};

const PLACEHOLDER_IMAGE = require("../assets/images/placeholder.jpg");

// Default error message for 404 errors
const DEFAULT_ERROR_MESSAGE =
	"It seems that your request is not successful. We apologize for the inconvenience.";

const NotFoundPage: React.FC = () => {
	const navigation =
		useNavigation<NativeStackNavigationProp<RootStackParamList>>();

	// Navigate back to home screen
	const goToHomePage = () => {
		navigation.navigate("HomePage");
	};

	return (
		<SafeAreaView style={styles.container}>
			<View style={styles.contentContainer}>
				<Text style={styles.errorCode}>404</Text>
				<Text style={styles.heading}>Oops!</Text>
				<Text style={styles.message}>{DEFAULT_ERROR_MESSAGE}</Text>

				<TouchableOpacity
					style={[
						styles.button,
						{ opacity: GLOBAL_STYLES.OPACITY.TOUCHABLE_DEFAULT },
					]}
					activeOpacity={GLOBAL_STYLES.OPACITY.TOUCHABLE_PRESSED}
					onPress={goToHomePage}
					hasTVPreferredFocus={true}
					accessibilityRole="button"
					accessibilityLabel="Go back to home page"
				>
					<Text style={styles.buttonText}>← Back home</Text>
				</TouchableOpacity>
			</View>

			<View style={styles.imageContainer}>
				<Image
					source={PLACEHOLDER_IMAGE}
					style={styles.image}
					resizeMode="cover"
				/>
			</View>
		</SafeAreaView>
	);
};

const styles = StyleSheet.create({
	container: {
		flex: 1,
		flexDirection: "row",
		backgroundColor: GLOBAL_STYLES.COLORS.PAGE_BACKGROUND,
	},
	contentContainer: {
		flex: LAYOUT.CONTENT_FLEX,
		padding: scale(40),
		justifyContent: "center",
		alignItems: "flex-start",
	},
	errorTextContainer: {
		marginBottom: scale(40),
	},
	errorCode: {
		color: "#ff3b30",
		fontSize: scale(36),
		fontWeight: "bold",
		marginBottom: scale(8),
	},
	heading: {
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		fontSize: scale(56),
		fontWeight: "bold",
		marginBottom: scale(20),
	},
	message: {
		color: GLOBAL_STYLES.COLORS.TEXT_SECONDARY,
		fontSize: scale(28),
		fontWeight: "500",
		lineHeight: scale(36),
		marginBottom: scale(32),
		maxWidth: scale(550),
	},
	button: {
		paddingVertical: scale(12),
		paddingHorizontal: scale(20),
		backgroundColor: "transparent",
		borderWidth: scale(2),
		borderColor: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		borderRadius: scale(24),
	},
	buttonText: {
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		fontSize: scale(24),
		fontWeight: "500",
	},
	imageContainer: {
		flex: LAYOUT.IMAGE_FLEX,
		overflow: "hidden",
	},
	image: {
		width: "100%",
		height: "100%",
	},
});

export default NotFoundPage;
